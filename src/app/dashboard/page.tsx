"use client";

import MainLayout from "@/layouts/main-layout";
import React from "react";
import { useAuth } from "@/hooks/useAuth";
import { Loader2, User, Mail, Shield } from "lucide-react";

export default function DashboardPage() {
  const { user, loading, isAuthenticated, logout } = useAuth();

  if (loading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (!isAuthenticated || !user) {
    return (
      <MainLayout>
        <div className="text-center py-12">
          <h2 className="text-xl text-red-400">Authentication required</h2>
          <p className="text-gray-400 mt-2">
            Please log in to access the dashboard.
          </p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Welcome Section */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h1 className="text-2xl font-bold text-white mb-2">
            Welcome back, {user.email}!
          </h1>
          <p className="text-gray-400">
            You're successfully logged in to the admin panel.
          </p>
        </div>

        {/* User Info Card */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
            <User className="h-5 w-5" />
            User Information
          </h2>

          <div className="space-y-3">
            <div className="flex items-center gap-3 text-gray-300">
              <Mail className="h-4 w-4 text-gray-400" />
              <span className="font-medium">Email:</span>
              <span>{user.email}</span>
            </div>

            <div className="flex items-center gap-3 text-gray-300">
              <Shield className="h-4 w-4 text-gray-400" />
              <span className="font-medium">Role:</span>
              <span className="capitalize bg-primary/20 text-primary px-2 py-1 rounded text-sm">
                {user.role}
              </span>
            </div>

            <div className="flex items-center gap-3 text-gray-300">
              <User className="h-4 w-4 text-gray-400" />
              <span className="font-medium">User ID:</span>
              <span className="font-mono text-sm">{user.id}</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-gray-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-white mb-4">
            Quick Actions
          </h2>

          <div className="flex gap-4">
            <button
              onClick={logout}
              className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
