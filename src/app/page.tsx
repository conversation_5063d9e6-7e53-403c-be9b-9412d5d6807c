"use client";
import { Input } from "@/components/ui/input";
import AuthLayout from "@/layouts/auth-layout";
import { useState } from "react";
import { Eye, EyeOff, Loader2 } from "lucide-react";
import { Auth } from "@/models/auth";
import { useRouter } from "next/navigation";
import AuthService, { ApiError } from "@/service/auth.service";

export default function Home() {
  const [formData, setFormData] = useState<Auth>({
    email: "",
    password: "",
  });
  const [showPassword, setShowPassword] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const router = useRouter();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
    // Clear error when user starts typing
    if (error) setError("");
  };

  const togglePassword = () => {
    setShowPassword((prev) => !prev);
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (!formData.email || !formData.password) {
      setError("Please fill in all fields");
      return;
    }

    setLoading(true);
    setError("");

    try {
      const response = await AuthService.login(formData);

      if (response.success && response.data) {
        // Store authentication data
        AuthService.storeAuthData(response.data.token, response.data.user);

        // Redirect to dashboard
        router.push("/dashboard");
      } else {
        setError(response.message || "Login failed");
      }
    } catch (error) {
      if (error instanceof ApiError) {
        setError(error.message);
      } else {
        setError("An unexpected error occurred. Please try again.");
      }
      console.error("Login error:", error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <AuthLayout>
      <div className="flex flex-col gap-4 w-full max-w-md">
        <h1 className="text-3xl text-center font-bold">Admin Login</h1>
        <div className="flex text-lg text-center justify-center gap-4">
          Please enter your credentials to access the admin panel.
        </div>

        <form onSubmit={handleLogin} className="flex flex-col gap-4">
          {error && (
            <div className="bg-red-500/10 border border-red-500/20 text-red-400 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          <Input
            type="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            placeholder="Email"
            disabled={loading}
            required
            className="bg-gray-800 border-gray-700 text-white placeholder-gray-400"
          />

          <div className="relative">
            <Input
              type={showPassword ? "text" : "password"}
              name="password"
              value={formData.password}
              onChange={handleChange}
              placeholder="Password"
              disabled={loading}
              required
              className="pr-10 bg-gray-800 border-gray-700 text-white placeholder-gray-400"
            />
            <button
              type="button"
              onClick={togglePassword}
              disabled={loading}
              className="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-300 disabled:opacity-50"
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5" />
              ) : (
                <Eye className="h-5 w-5" />
              )}
            </button>
          </div>

          <button
            type="submit"
            disabled={loading}
            className="bg-primary text-white rounded-lg p-3 hover:bg-primary/80 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 font-medium"
          >
            {loading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                Signing in...
              </>
            ) : (
              "Login"
            )}
          </button>
        </form>
      </div>
    </AuthLayout>
  );
}
