import { NextRequest, NextResponse } from "next/server";
import axios from "axios";

export async function POST(request: NextRequest) {
  try {
    // Parse request body
    const { email, password } = await request.json();

    // Basic validation
    if (!email || !password) {
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }

    console.log("Login request received:", email, password);

    // Make API request to backend
    const res = await axios.post("http://13.204.159.70:8081/api/auth", {
      email,
      password,
    });
    console.log("Login response received:", res.data);

    // Return success response from backend
    return NextResponse.json(res.data, { status: res.status });
  } catch (error: any) {
    console.error("Error processing login request:", error);

    // Handle Axios errors separately for better clarity
    if (error.response) {
      return NextResponse.json(
        {
          message: error.response.data?.message || "Invalid credentials",
        },
        { status: error.response.status || 400 }
      );
    }

    return NextResponse.json(
      { message: "An error occurred. Please try again later." },
      { status: 500 }
    );
  }
}
