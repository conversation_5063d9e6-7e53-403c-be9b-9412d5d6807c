# Authentication API Documentation

## Overview

This document describes the authentication API implementation for the webapp admin panel. The API provides secure login functionality with proper error handling and validation.

## Environment Configuration

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Private API URL (server-side only)
API_URL=http://*************:8081/api

# JWT Secret for token signing (change in production)
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

**Important Notes:**
- `API_URL` is now private (not `NEXT_PUBLIC_API_URL`) for security
- The API URL is only accessible on the server-side
- Always use a strong, unique JWT secret in production

## API Endpoints

### POST /api/auth

Login endpoint for user authentication.

#### Request

```typescript
POST /api/auth
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "userpassword"
}
```

#### Response

**Success (200):**
```typescript
{
  "success": true,
  "message": "Login successful",
  "data": {
    "token": "jwt-token-here",
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
}
```

**Error (400/401/422/500):**
```typescript
{
  "success": false,
  "message": "Error description",
  "error": "Error description"
}
```

#### Error Codes

- `400` - Bad Request (invalid data format)
- `401` - Unauthorized (invalid credentials)
- `422` - Validation Error (missing/invalid fields)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error
- `503` - Service Unavailable (external API down)

## Frontend Usage

### Using the AuthService

```typescript
import AuthService from '@/service/auth.service';

// Login
try {
  const response = await AuthService.login({
    email: '<EMAIL>',
    password: 'password'
  });
  
  if (response.success) {
    // Store auth data automatically
    AuthService.storeAuthData(response.data.token, response.data.user);
    // Redirect to dashboard
  }
} catch (error) {
  console.error('Login failed:', error.message);
}

// Check authentication
const isLoggedIn = AuthService.isAuthenticated();

// Get user data
const userData = AuthService.getUserData();

// Logout
AuthService.clearAuthData();
```

### Using the useAuth Hook

```typescript
import { useAuth } from '@/hooks/useAuth';

function MyComponent() {
  const { user, isAuthenticated, loading, login, logout, error } = useAuth();

  const handleLogin = async () => {
    const result = await login('<EMAIL>', 'password');
    if (result.success) {
      // Login successful
    }
  };

  if (loading) return <div>Loading...</div>;
  if (!isAuthenticated) return <div>Please login</div>;

  return (
    <div>
      <p>Welcome, {user.email}!</p>
      <button onClick={logout}>Logout</button>
    </div>
  );
}
```

## Security Features

### Input Validation
- Email format validation
- Password length requirements (minimum 6 characters)
- Request body validation

### Error Handling
- Comprehensive error messages
- Proper HTTP status codes
- Network error handling
- External API error handling

### Security Headers
- Content-Type validation
- CORS handling
- Method restrictions (only POST allowed)

### Authentication Flow
1. User submits credentials
2. Server validates input
3. Server forwards request to external API
4. External API validates credentials
5. Server returns JWT token and user data
6. Client stores token for future requests

## Middleware Protection

Routes are automatically protected using Next.js middleware:

**Protected Routes:**
- `/dashboard`
- `/subscribers`
- `/leUsers`
- `/content`
- `/finance`
- `/sponsorAds`
- `/results`
- `/auditLog`
- `/masterData`

**Public Routes:**
- `/` (login page)
- `/api/auth`

## Testing the API

### Using curl

```bash
# Login request
curl -X POST http://localhost:3000/api/auth \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "testpassword"
  }'
```

### Using Postman

1. Set method to POST
2. URL: `http://localhost:3000/api/auth`
3. Headers: `Content-Type: application/json`
4. Body (raw JSON):
```json
{
  "email": "<EMAIL>",
  "password": "testpassword"
}
```

## Error Scenarios

The API handles various error scenarios:

1. **Missing fields**: Returns validation error
2. **Invalid email format**: Returns validation error
3. **External API down**: Returns service unavailable
4. **Invalid credentials**: Returns unauthorized
5. **Network issues**: Returns connection error
6. **Malformed JSON**: Returns bad request

## Best Practices

1. **Always validate input** on both client and server
2. **Use HTTPS** in production
3. **Store tokens securely** (httpOnly cookies recommended for production)
4. **Implement rate limiting** to prevent brute force attacks
5. **Log security events** for monitoring
6. **Use environment variables** for sensitive configuration
7. **Implement token refresh** for long-lived sessions
